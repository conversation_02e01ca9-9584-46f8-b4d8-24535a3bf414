"use client";

import Image from "next/image";
import { useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Cloud, CloudRain, Sun, Snowflake, Droplets, Thermometer, Gauge } from "lucide-react";

type WeatherCardProps = {
  image: string;
  condition: string;
  name: string;
  country?: string;
  temperature: number;
  windspeed: number;
  feelsLike?: number | null;
  humidity?: number | null;
  pressure?: number | null;
  unit: "metric" | "imperial";
  imageHeight?: number;
};

export default function WeatherCard(props: WeatherCardProps) {
  const locationLabel = useMemo(() => {
    if (props.country) return `${props.name}, ${props.country}`;
    return props.name;
  }, [props.name, props.country]);

  const ConditionIcon = useMemo(() => {
    const c = props.condition.toLowerCase();
    if (c.includes("rain") || c.includes("drizzle")) return CloudRain;
    if (c.includes("snow")) return Snowflake;
    if (c.includes("cloud")) return Cloud;
    return Sun;
  }, [props.condition]);

  return (
    <Card className="w-full max-w-4xl overflow-hidden border border-border shadow-xl">
      <CardContent className="p-0">
        <div className="grid grid-cols-1 md:grid-cols-2">
          <div className="relative" style={{ height: props.imageHeight ?? 280 }}>
            <Image
              src={props.image}
              alt={props.condition}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, 50vw"
              priority
            />
          </div>
          <div className="p-6 flex flex-col gap-4">
            <div>
              <h2 className="text-2xl font-semibold tracking-tight flex items-center gap-2">
                {locationLabel}
                <Badge variant="secondary" className="ml-1 text-xs">
                  {props.unit === "imperial" ? "Imperial" : "Metric"}
                </Badge>
              </h2>
              <p className="text-muted-foreground mt-1 flex items-center gap-2">
                <ConditionIcon className="h-4 w-4" /> {props.condition}
              </p>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="rounded-lg bg-secondary p-4">
                <p className="text-sm text-muted-foreground">Temperature</p>
                <p className="text-3xl font-bold">
                  {Math.round(props.temperature)}{props.unit === "imperial" ? "°F" : "°C"}
                </p>
              </div>
              <div className="rounded-lg bg-secondary p-4">
                <p className="text-sm text-muted-foreground">Wind</p>
                <p className="text-3xl font-bold">
                  {Math.round(props.windspeed)} {props.unit === "imperial" ? "mph" : "km/h"}
                </p>
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="rounded-lg bg-secondary p-3">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Thermometer className="h-4 w-4" /> Feels like
                </div>
                <div className="text-xl font-semibold">
                  {props.feelsLike != null
                    ? `${Math.round(props.feelsLike)}${props.unit === "imperial" ? "°F" : "°C"}`
                    : "-"}
                </div>
              </div>
              <div className="rounded-lg bg-secondary p-3">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Droplets className="h-4 w-4" /> Humidity
                </div>
                <div className="text-xl font-semibold">
                  {props.humidity != null ? `${Math.round(props.humidity)}%` : "-"}
                </div>
              </div>
              <div className="rounded-lg bg-secondary p-3">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Gauge className="h-4 w-4" /> Pressure
                </div>
                <div className="text-xl font-semibold">
                  {props.pressure != null ? `${Math.round(props.pressure)} hPa` : "-"}
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}


