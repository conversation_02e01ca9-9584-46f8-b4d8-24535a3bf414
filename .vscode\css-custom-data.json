{"version": 1.1, "atDirectives": [{"name": "@apply", "description": "Use @apply to inline any existing utility classes into your own custom CSS.", "references": [{"name": "Tailwind CSS Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#apply"}]}, {"name": "@layer", "description": "Use @layer to tell Tailwind which \"bucket\" a set of custom styles belong to.", "references": [{"name": "Tailwind CSS Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#layer"}]}, {"name": "@theme", "description": "Use @theme to define your design tokens inline in CSS.", "references": [{"name": "Tailwind CSS v4 Documentation", "url": "https://tailwindcss.com/docs/v4-beta#theme-configuration"}]}, {"name": "@custom-variant", "description": "Use @custom-variant to define custom variants for Tailwind CSS.", "references": [{"name": "Tailwind CSS v4 Documentation", "url": "https://tailwindcss.com/docs/v4-beta#custom-variants"}]}, {"name": "@import", "description": "Use @import to include other CSS files or Tailwind CSS base styles.", "references": [{"name": "Tailwind CSS Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#import"}]}, {"name": "@config", "description": "Use @config to specify a custom configuration file path.", "references": [{"name": "Tailwind CSS Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#config"}]}]}