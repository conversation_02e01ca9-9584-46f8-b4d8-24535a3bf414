"use client";

import { useEffect, useRef, useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { LocateFixed, Search } from "lucide-react";

type SearchResult = {
  name: string;
  country: string;
  admin1: string;
  latitude: number;
  longitude: number;
};

type Props = {
  unit: "metric" | "imperial";
  onSelectQuery: (text: string) => void;
  onSelectCoords: (lat: number, lon: number) => void;
  loading: boolean;
};

export default function SearchBox(props: Props) {
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [pending, setPending] = useState(false);
  const abortRef = useRef<AbortController | null>(null);

  useEffect(() => {
    if (!value.trim()) {
      setResults([]);
      return;
    }
    setPending(true);
    abortRef.current?.abort();
    const ctl = new AbortController();
    abortRef.current = ctl;
    const id = setTimeout(async () => {
      try {
        const res = await fetch(`/api/search?q=${encodeURIComponent(value)}`, { signal: ctl.signal });
        const json = (await res.json()) as { results: SearchResult[] };
        setResults(json.results || []);
      } catch {}
      setPending(false);
    }, 250);
    return () => {
      clearTimeout(id);
      ctl.abort();
    };
  }, [value]);

  return (
    <div className="flex gap-2 w-full">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="relative flex-1">
            <Input
              placeholder="Search city or ZIP"
              value={value}
              onChange={(e) => {
                setValue(e.target.value);
                setOpen(true);
              }}
              onFocus={() => setOpen(true)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  setOpen(false);
                  props.onSelectQuery(value);
                }
              }}
            />
            <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          </div>
        </PopoverTrigger>
        <PopoverContent className="p-0 w-[min(560px,90vw)]" align="start">
          <Command shouldFilter={false}>
            <CommandInput placeholder={pending ? "Searching..." : "Type to search"} value={value} onValueChange={setValue} />
            <CommandList>
              <CommandEmpty>No results</CommandEmpty>
              <CommandGroup heading="Suggestions">
                {results.map((r, idx) => (
                  <CommandItem
                    key={`${r.name}-${idx}`}
                    value={`${r.name}, ${r.admin1 || r.country}`}
                    onSelect={() => {
                      setOpen(false);
                      props.onSelectQuery(r.name);
                    }}
                  >
                    <div className="flex flex-col">
                      <span className="font-medium">{r.name}</span>
                      <span className="text-xs text-muted-foreground">{[r.admin1, r.country].filter(Boolean).join(", ")}</span>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      <Button onClick={() => props.onSelectQuery(value)} disabled={props.loading}>
        {props.loading ? "Loading" : "Search"}
      </Button>
      <Button variant="secondary" title="Use current location" onClick={() => {
        if (!navigator.geolocation) return;
        navigator.geolocation.getCurrentPosition((pos) => {
          props.onSelectCoords(pos.coords.latitude, pos.coords.longitude);
        });
      }}>
        <LocateFixed className="h-4 w-4" />
      </Button>
    </div>
  );
}


