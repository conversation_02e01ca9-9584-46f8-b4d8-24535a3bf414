import { NextResponse } from "next/server";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");
    if (!query) {
      return NextResponse.json({ results: [] });
    }
    const res = await fetch(
      `https://geocoding-api.open-meteo.com/v1/search?name=${encodeURIComponent(query)}&count=7&language=en&format=json`,
      { cache: "no-store" }
    );
    if (!res.ok) {
      return NextResponse.json({ results: [] });
    }
    const json = (await res.json()) as unknown as {
      results?: Array<{
        id?: number;
        name: string;
        country?: string;
        admin1?: string;
        latitude: number;
        longitude: number;
      }>;
    };
    const results = (json.results ?? []).map((r) => ({
      name: r.name,
      country: r.country ?? "",
      admin1: r.admin1 ?? "",
      latitude: r.latitude,
      longitude: r.longitude,
    }));
    return NextResponse.json({ results });
  } catch {
    return NextResponse.json({ results: [] });
  }
}


