export type Condition = "Sunny" | "Cloudy" | "Rain" | "Snow" | "Fog";



export function backgroundForCondition(condition: Condition, isDay: number | boolean): string {
  const day = typeof isDay === "boolean" ? (isDay ? 1 : 0) : isDay;
  const isDaytime = day === 1;
  if (!isDaytime) return "bg-gradient-to-b from-slate-800 via-slate-900 to-black";
  switch (condition) {
    case "Rain":
      return "bg-gradient-to-b from-sky-200 via-sky-100 to-slate-50";
    case "Cloudy":
      return "bg-gradient-to-b from-slate-100 via-slate-50 to-white";
    case "Snow":
      return "bg-gradient-to-b from-blue-100 via-white to-white";
    case "Fog":
      return "bg-gradient-to-b from-zinc-100 via-zinc-50 to-white";
    default:
      return "bg-gradient-to-b from-amber-100 via-orange-50 to-white";
  }
}


