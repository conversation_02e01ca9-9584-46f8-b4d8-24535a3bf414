import { NextResponse } from "next/server";

type Units = "metric" | "imperial";

function conditionLabel(weathercode: number): string {
  const rainyCodes = new Set<number>([
    51, 53, 55, 56, 57, 61, 63, 65, 66, 67, 80, 81, 82, 95, 96, 99,
  ]);
  const snowyCodes = new Set<number>([71, 73, 75, 77, 85, 86]);
  const fogCodes = new Set<number>([45, 48]);
  const cloudyCodes = new Set<number>([1, 2, 3]);

  if (rainyCodes.has(weathercode)) return "Rain";
  if (snowyCodes.has(weathercode)) return "Snow";
  if (fogCodes.has(weathercode)) return "Fog";
  if (cloudyCodes.has(weathercode)) return "Cloudy";
  return "Sunny";
}

function pickImageForWeatherCode(weathercode: number): { image: string; condition: string } {
  const label = conditionLabel(weathercode);
  if (label === "Rain" || label === "Snow") {
    return { image: "/image/rainy.png", condition: label };
  }
  return { image: "/image/sunny.png", condition: label };
}

function unitParams(units: Units): { temperature: string; wind: string } {
  return units === "imperial"
    ? { temperature: "fahrenheit", wind: "mph" }
    : { temperature: "celsius", wind: "kmh" };
}

function aqiCategory(aqi: number | null): string | null {
  if (aqi == null) return null;
  if (aqi <= 50) return "Good";
  if (aqi <= 100) return "Moderate";
  if (aqi <= 150) return "Unhealthy for Sensitive";
  if (aqi <= 200) return "Unhealthy";
  if (aqi <= 300) return "Very Unhealthy";
  return "Hazardous";
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");
    const unitParam = (searchParams.get("unit") as Units | null) ?? "metric";
    const latParam = searchParams.get("lat");
    const lonParam = searchParams.get("lon");

    let latitude: number | null = null;
    let longitude: number | null = null;
    let name: string | null = null;
    let country: string | null = null;

    if (latParam && lonParam) {
      latitude = Number(latParam);
      longitude = Number(lonParam);
      name = null;
      country = null;
    } else if (query) {
      const geoRes = await fetch(
        `https://geocoding-api.open-meteo.com/v1/search?name=${encodeURIComponent(
          query
        )}&count=1&language=en&format=json`,
        { cache: "no-store" }
      );
      if (!geoRes.ok) {
        return NextResponse.json({ error: "Failed to fetch location" }, { status: 502 });
      }
      const geo = (await geoRes.json()) as unknown as {
        results?: Array<{ name: string; country?: string; latitude: number; longitude: number }>;
      };
      if (!geo || !geo.results || geo.results.length === 0) {
        return NextResponse.json({ error: "Location not found" }, { status: 404 });
      }
      const place = geo.results[0];
      latitude = place.latitude;
      longitude = place.longitude;
      name = place.name;
      country = place.country ?? "";
    } else {
      return NextResponse.json({ error: "Provide either 'q' or 'lat' and 'lon'" }, { status: 400 });
    }

    const units = unitParam === "imperial" ? "imperial" : "metric";
    const unitCfg = unitParams(units);
    const hourlyParams = [
      "temperature_2m",
      "apparent_temperature",
      "relative_humidity_2m",
      "pressure_msl",
      "weather_code",
    ].join(",");
    const dailyParams = [
      "temperature_2m_max",
      "temperature_2m_min",
      "weather_code",
      "sunrise",
      "sunset",
    ].join(",");

    const url = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}` +
      `&current_weather=true&hourly=${hourlyParams}&daily=${dailyParams}&forecast_days=7&timezone=auto` +
      `&temperature_unit=${unitCfg.temperature}&wind_speed_unit=${unitCfg.wind}`;

    const weatherRes = await fetch(url, { cache: "no-store" });
    if (!weatherRes.ok) {
      return NextResponse.json({ error: "Failed to fetch weather" }, { status: 502 });
    }
    const weather = (await weatherRes.json()) as unknown as {
      current_weather?: {
        temperature: number;
        windspeed: number;
        weathercode: number;
        is_day: number;
        time: string;
      };
      hourly?: {
        time: string[];
        temperature_2m: number[];
        apparent_temperature: number[];
        relative_humidity_2m: number[];
        pressure_msl: number[];
        weather_code: number[];
      };
      daily?: {
        time: string[];
        temperature_2m_max: number[];
        temperature_2m_min: number[];
        weather_code: number[];
        sunrise: string[];
        sunset: string[];
      };
    };

    const current = weather.current_weather;
    if (!current) {
      return NextResponse.json({ error: "No current weather available" }, { status: 404 });
    }

    // Map to nearest hourly record for details
    let feelsLike: number | null = null;
    let humidity: number | null = null;
    let pressure: number | null = null;
    let sunrise: string | null = null;
    let sunset: string | null = null;
    const hourlyNext: Array<{ time: string; temperature: number; weathercode: number }> = [];
    const dailyNext: Array<{ date: string; max: number; min: number; weathercode: number }> = [];

    if (weather.hourly && weather.hourly.time.length > 0) {
      const tIndex = weather.hourly.time.indexOf(current.time);
      const idx = tIndex >= 0 ? tIndex : 0;
      feelsLike = weather.hourly.apparent_temperature[idx] ?? null;
      humidity = weather.hourly.relative_humidity_2m[idx] ?? null;
      pressure = weather.hourly.pressure_msl[idx] ?? null;

      // Build next 12 hours forecast
      const start = idx;
      const end = Math.min(weather.hourly.time.length, start + 12);
      for (let i = start; i < end; i += 1) {
        hourlyNext.push({
          time: weather.hourly.time[i],
          temperature: weather.hourly.temperature_2m[i],
          weathercode: weather.hourly.weather_code[i],
        });
      }
    }

    if (weather.daily && weather.daily.time.length > 0) {
      sunrise = weather.daily.sunrise[0] ?? null;
      sunset = weather.daily.sunset[0] ?? null;
      const len = Math.min(weather.daily.time.length, 7);
      for (let i = 0; i < len; i += 1) {
        dailyNext.push({
          date: weather.daily.time[i],
          max: weather.daily.temperature_2m_max[i],
          min: weather.daily.temperature_2m_min[i],
          weathercode: weather.daily.weather_code[i],
        });
      }
    }

    // Air quality
    let aqiIndex: number | null = null;
    try {
      const aqiRes = await fetch(
        `https://air-quality-api.open-meteo.com/v1/air-quality?latitude=${latitude}&longitude=${longitude}&hourly=us_aqi&timezone=auto`,
        { cache: "no-store" }
      );
      if (aqiRes.ok) {
        const aqiJson = (await aqiRes.json()) as unknown as {
          hourly?: { time: string[]; us_aqi: number[] };
        };
        if (aqiJson.hourly && aqiJson.hourly.time.length > 0) {
          const idx = aqiJson.hourly.time.indexOf(current.time);
          const aqiAt = idx >= 0 ? aqiJson.hourly.us_aqi[idx] : aqiJson.hourly.us_aqi[aqiJson.hourly.us_aqi.length - 1];
          aqiIndex = typeof aqiAt === "number" ? aqiAt : null;
        }
      }
    } catch {
      aqiIndex = null;
    }

    const { image, condition } = pickImageForWeatherCode(current.weathercode);

    return NextResponse.json({
      name: name ?? "",
      country: country ?? "",
      latitude,
      longitude,
      unit: units,
      current: {
        temperature: current.temperature,
        windspeed: current.windspeed,
        weathercode: current.weathercode,
        is_day: current.is_day,
        time: current.time,
      },
      details: {
        feels_like: feelsLike,
        humidity,
        pressure,
      },
      sunrise,
      sunset,
      hourly: hourlyNext,
      daily: dailyNext,
      aqi: aqiIndex != null ? { index: aqiIndex, category: aqiCategory(aqiIndex) } : null,
      image,
      condition,
    });
  } catch {
    return NextResponse.json({ error: "Unexpected server error" }, { status: 500 });
  }
}


