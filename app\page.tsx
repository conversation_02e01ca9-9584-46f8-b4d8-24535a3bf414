"use client";

import React, { useState, useEffect } from "react";
import WeatherCard from "@/components/WeatherCard";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MapPin, Star, Sun, CloudRain } from "lucide-react";
import { backgroundForCondition } from "@/lib/weather";
import type { Condition } from "@/lib/weather";
import SearchBox from "@/components/SearchBox";

interface WeatherResponse {
  name: string;
  country: string;
  latitude: number;
  longitude: number;
  unit: "metric" | "imperial";
  current: {
    temperature: number;
    windspeed: number;
    weathercode: number;
    is_day: number;
    time: string;
  };
  details?: {
    feels_like: number | null;
    humidity: number | null;
    pressure: number | null;
  };
  sunrise?: string | null;
  sunset?: string | null;
  hourly?: Array<{ time: string; temperature: number; weathercode: number }>;
  daily?: Array<{ date: string; max: number; min: number; weathercode: number }>;
  aqi?: { index: number; category: string } | null;
  image: string;
  condition: Condition;
}

export default function Home() {
  const [query, setQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<WeatherResponse | null>(null);
  const [unit, setUnit] = useState<"metric" | "imperial">("metric");
  const [favorites, setFavorites] = useState<string[]>(() => {
    try {
      const raw = localStorage.getItem("favorites");
      return raw ? (JSON.parse(raw) as string[]) : [];
    } catch {
      return [];
    }
  });
  const [lastSource, setLastSource] = useState<
    | { type: "q"; q: string }
    | { type: "geo"; lat: number; lon: number }
    | null
  >(null);

  async function onSearch() {
    if (!query.trim()) return;
    setLoading(true);
    setError(null);
    try {
      const res = await fetch(`/api/weather?q=${encodeURIComponent(query)}&unit=${unit}`, { cache: "no-store" });
      const json = await res.json();
      if (!res.ok) {
        setError(json.error || "Failed to fetch weather");
        setData(null);
        return;
      }
      setData(json);
      setLastSource({ type: "q", q: query });
      try { localStorage.setItem("lastWeather", JSON.stringify(json)); } catch {}
    } catch {
      try {
        const cached = localStorage.getItem("lastWeather");
        if (cached) setData(JSON.parse(cached) as WeatherResponse);
        else setError("Network error");
      } catch {
        setError("Network error");
      }
    } finally {
      setLoading(false);
    }
  }

  // GPS handled inside SearchBox's onSelectCoords callback

  function toggleFavorite(city: string) {
    setFavorites((prev) => {
      const exists = prev.includes(city);
      const next = exists ? prev.filter((c) => c !== city) : [...prev, city];
      try {
        localStorage.setItem("favorites", JSON.stringify(next));
      } catch {}
      return next;
    });
  }

  // Refetch when unit changes
  useEffect(() => {
    if (!lastSource) return;
    (async () => {
      try {
        let url = "";
        if (lastSource.type === "q") {
          url = `/api/weather?q=${encodeURIComponent(lastSource.q)}&unit=${unit}`;
        } else {
          url = `/api/weather?lat=${lastSource.lat}&lon=${lastSource.lon}&unit=${unit}`;
        }
        const res = await fetch(url, { cache: "no-store" });
        const json = await res.json();
        if (res.ok) setData(json);
      } catch {}
    })();
  }, [unit, lastSource]);

  const bgClass = data ? backgroundForCondition(data.condition, data.current.is_day) : "bg-background";
  return (
    <div className={`min-h-screen ${bgClass} text-foreground transition-colors`}>
      <main className="container mx-auto max-w-6xl px-4 lg:px-6 py-10">
        <h1 className="text-3xl font-bold tracking-tight">Weather</h1>
        <p className="text-muted-foreground mt-1">Search a city or use your location to view weather details.</p>
        <div className="mt-6">
          <SearchBox
            unit={unit}
            loading={loading}
            onSelectQuery={(text) => { setQuery(text); onSearch(); }}
            onSelectCoords={(lat, lon) => {
              setQuery("");
              setLoading(true);
              (async () => {
                try {
                  const res = await fetch(`/api/weather?lat=${lat}&lon=${lon}&unit=${unit}`, { cache: "no-store" });
                  const json = await res.json();
                  if (res.ok) setData(json); else setError(json.error || "Failed to fetch");
                } finally {
                  setLoading(false);
                }
              })();
            }}
          />
        </div>

        <div className="mt-3 flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Sun className="h-4 w-4" />
            <Switch
              checked={unit === "imperial"}
              onCheckedChange={(checked) => setUnit(checked ? "imperial" : "metric")}
            />
            <CloudRain className="h-4 w-4" />
            <span className="text-sm text-muted-foreground">{unit === "imperial" ? "°F / mph" : "°C / km/h"}</span>
          </div>
        </div>

        {error && <p className="text-red-600 mt-4">{error}</p>}

        {data && (
          <div className="mt-8">
            <div className="flex items-center justify-end">
              <Button variant="ghost" size="icon" onClick={() => toggleFavorite(data.name)} title="Toggle favorite">
                <Star className={`h-5 w-5 ${favorites.includes(data.name) ? "fill-current" : ""}`} />
              </Button>
            </div>
            <WeatherCard
              image={data.image}
              condition={data.condition}
              name={data.name}
              country={data.country}
              temperature={data.current.temperature}
              windspeed={data.current.windspeed}
              feelsLike={data.details?.feels_like ?? null}
              humidity={data.details?.humidity ?? null}
              pressure={data.details?.pressure ?? null}
              unit={unit}
              imageHeight={320}
            />

            <div className="mt-6 grid gap-6">
              {data.aqi && (
                <div className="flex items-center gap-3">
                  <Badge variant="secondary">AQI: {data.aqi.index}</Badge>
                  <span className="text-sm text-muted-foreground">{data.aqi.category}</span>
                </div>
              )}
              <Separator />

              {data.sunrise && data.sunset && (
                <div className="text-sm text-muted-foreground">
                  Sunrise: {new Date(data.sunrise).toLocaleTimeString()} · Sunset: {new Date(data.sunset).toLocaleTimeString()}
                </div>
              )}

              {data.hourly && data.hourly.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold">Next hours</h3>
                  <ScrollArea className="w-full">
                    <div className="flex gap-3 py-3">
                      {data.hourly.map((h, i) => (
                        <div key={i} className="min-w-28 rounded-lg border p-3 text-center">
                          <div className="text-xs text-muted-foreground">{new Date(h.time).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}</div>
                          <div className="text-base font-semibold">{Math.round(h.temperature)}{unit === "imperial" ? "°F" : "°C"}</div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}

              {data.daily && data.daily.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold">7-day forecast</h3>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-7 gap-3">
                    {data.daily.map((d, i) => (
                      <div key={i} className="rounded-lg border p-3 text-center">
                        <div className="text-xs text-muted-foreground">{new Date(d.date).toLocaleDateString(undefined, { weekday: "short" })}</div>
                        <div className="text-base font-semibold">
                          {Math.round(d.max)} / {Math.round(d.min)}{unit === "imperial" ? "°F" : "°C"}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {favorites.length > 0 && (
          <div className="mt-10">
            <h3 className="text-lg font-semibold flex items-center gap-2"><Star className="h-4 w-4" /> Favorites</h3>
            <div className="mt-3 flex flex-wrap gap-2">
              {favorites.map((city) => (
                <Badge key={city} variant="secondary" className="cursor-pointer" onClick={() => { setQuery(city); }}>
                  <MapPin className="h-3 w-3 mr-1" /> {city}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
